# Vanna + Gemini Balance Web部署指南

## 概述

本指南介绍如何部署Vanna + Gemini Balance的Web界面，提供完整的自然语言到SQL转换的Web应用。

## 🚀 快速启动

### 方法1: 完整版Web应用

```bash
# 启动完整功能的Web应用
python3 vanna_web_app.py
```

### 方法2: 简化版Web应用

```bash
# 启动简化版Web应用（推荐用于测试）
python3 simple_web_app.py
```

## 📋 依赖要求

### 必需依赖
```bash
pip install flask flask-sock flasgger
```

### 可选依赖（用于增强功能）
```bash
pip install plotly pandas
```

## 🌐 访问Web界面

启动成功后，在浏览器中访问：
- **本地访问**: http://localhost:8084
- **网络访问**: http://0.0.0.0:8084

## 🎯 Web界面功能

### 核心功能
- ✅ **自然语言输入** - 输入中文或英文问题
- ✅ **SQL生成** - 自动生成对应的SQL查询
- ✅ **SQL编辑** - 可以手动编辑生成的SQL
- ✅ **智能建议** - 提供相关问题建议

### 高级功能
- ✅ **数据执行** - 连接数据库后可直接执行SQL
- ✅ **结果展示** - 表格形式展示查询结果
- ✅ **数据可视化** - 自动生成图表
- ✅ **CSV下载** - 下载查询结果
- ✅ **自动修复** - 自动修复SQL语法错误
- ✅ **结果总结** - AI总结查询结果
- ✅ **后续问题** - 基于结果生成后续问题

### 管理功能
- ✅ **训练数据管理** - 查看和管理训练数据
- ✅ **添加表结构** - 动态添加数据库表结构
- ✅ **添加示例** - 添加问题-SQL训练示例

## 🔧 配置选项

### 基本配置
```python
config = {
    "base_url": "你的Gemini Balance服务地址",
    "api_key": "你的API密钥",
    "model": "gemini-2.5-pro",  # 或其他模型
    "temperature": 0.7,
    "path": "./chroma_db"  # 向量数据库路径
}
```

### Web应用配置
```python
app = VannaFlaskApp(
    vn=vn,
    debug=True,                    # 调试模式
    allow_llm_to_see_data=True,    # 允许LLM查看数据
    title="自定义标题",
    subtitle="自定义副标题",
    logo="自定义Logo URL",
    show_training_data=True,       # 显示训练数据
    suggested_questions=True,      # 显示建议问题
    sql=True,                      # 显示SQL
    table=True,                    # 显示表格
    csv_download=True,             # CSV下载
    chart=True,                    # 图表功能
    auto_fix_sql=True,             # 自动修复SQL
    summarization=True             # 结果总结
)
```

## 🛠️ 部署选项

### 1. 本地开发部署
```python
app.run(host="localhost", port=8084, debug=True)
```

### 2. 局域网部署
```python
app.run(host="0.0.0.0", port=8084, debug=False)
```

### 3. 生产环境部署

#### 使用Gunicorn
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8084 vanna_web_app:app
```

#### 使用uWSGI
```bash
pip install uwsgi
uwsgi --http 0.0.0.0:8084 --module vanna_web_app:app --processes 4
```

### 4. Docker部署

创建 `Dockerfile`:
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY . .

RUN pip install -r requirements.txt

EXPOSE 8084

CMD ["python", "vanna_web_app.py"]
```

构建和运行:
```bash
docker build -t vanna-web .
docker run -p 8084:8084 vanna-web
```

## 🔒 安全配置

### 1. 认证配置
```python
from vanna.flask.auth import BasicAuth

auth = BasicAuth(username="admin", password="your_password")
app = VannaFlaskApp(vn=vn, auth=auth)
```

### 2. HTTPS配置
```python
app.run(host="0.0.0.0", port=8084, ssl_context='adhoc')
```

### 3. 环境变量配置
```bash
export GEMINI_API_KEY="your_api_key"
export GEMINI_BASE_URL="your_base_url"
```

## 📊 使用示例

### 1. 基本查询
- 输入: "有多少个用户？"
- 生成: `SELECT COUNT(*) FROM users;`

### 2. 复杂查询
- 输入: "每个月的销售趋势如何？"
- 生成: 包含时间分组和聚合的复杂SQL

### 3. 数据分析
- 输入: "哪些产品最受欢迎？"
- 生成: 包含JOIN和排序的分析SQL

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8084
   # 杀死进程
   kill -9 <PID>
   ```

2. **依赖缺失**
   ```bash
   pip install flask flask-sock flasgger plotly pandas
   ```

3. **权限问题**
   ```bash
   # 使用sudo运行（不推荐）
   sudo python3 vanna_web_app.py
   # 或更改端口到8080以上
   ```

4. **内存不足**
   - 使用简化版应用
   - 减少向量数据库大小
   - 增加系统内存

### 调试模式

启用详细日志:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🌟 最佳实践

### 1. 性能优化
- 使用生产级WSGI服务器
- 启用缓存机制
- 优化向量数据库大小

### 2. 安全建议
- 使用HTTPS
- 配置认证
- 限制访问IP
- 定期更新依赖

### 3. 监控建议
- 添加日志记录
- 监控资源使用
- 设置错误告警

## 📞 技术支持

如果遇到问题：
1. 检查依赖是否完整安装
2. 确认Gemini Balance服务可用
3. 查看控制台错误信息
4. 检查防火墙和端口设置

---

## 总结

通过以上配置，你可以：
- ✅ 快速部署Vanna Web界面
- ✅ 提供完整的自然语言到SQL功能
- ✅ 支持多种部署方式
- ✅ 具备生产环境部署能力

现在你可以通过Web界面轻松使用Vanna + Gemini Balance进行自然语言到SQL的转换了！
