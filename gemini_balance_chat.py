#!/usr/bin/env python3
"""
Gemini Balance 适配器 - 用于Vanna
支持通过OpenAI兼容API调用Gemini Balance服务
"""

import os
import requests
import json
from typing import List, Dict, Any

# 导入Vanna基类和向量存储
import sys
sys.path.append('src')
from vanna.base import VannaBase
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore


class GeminiBalanceChat(VannaBase):
    """
    Gemini Balance 聊天适配器
    使用OpenAI兼容的API格式调用Gemini Balance服务
    """
    
    def __init__(self, config=None):
        VannaBase.__init__(self, config=config)
        
        if config is None:
            raise ValueError("配置不能为空，需要提供base_url和api_key")
        
        # 必需的配置
        if "base_url" not in config:
            raise ValueError("配置中必须包含base_url")
        if "api_key" not in config:
            raise ValueError("配置中必须包含api_key")
        
        self.base_url = config["base_url"].rstrip('/')
        self.api_key = config["api_key"]
        
        # 可选配置
        self.model = config.get("model", "gemini-1.5-flash")
        self.temperature = config.get("temperature", 0.7)
        self.timeout = config.get("timeout", 30)
        
        # 验证服务可用性
        self._verify_service()
        
    def _verify_service(self):
        """验证服务是否可用"""
        try:
            response = requests.get(
                f"{self.base_url}/health", 
                timeout=10
            )
            if response.status_code != 200:
                raise RuntimeError(f"服务健康检查失败: {response.status_code}")
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"无法连接到Gemini Balance服务: {e}")
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            response = requests.get(
                f"{self.base_url}/v1/models", 
                headers=headers, 
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                models_data = response.json()
                return [model.get('id', '') for model in models_data.get("data", [])]
            else:
                print(f"获取模型列表失败: {response.status_code}")
                return []
        except Exception as e:
            print(f"获取模型列表时出错: {e}")
            return []
    
    def system_message(self, message: str) -> Dict[str, str]:
        """系统消息格式"""
        return {"role": "system", "content": message}
    
    def user_message(self, message: str) -> Dict[str, str]:
        """用户消息格式"""
        return {"role": "user", "content": message}
    
    def assistant_message(self, message: str) -> Dict[str, str]:
        """助手消息格式"""
        return {"role": "assistant", "content": message}
    
    def submit_prompt(self, prompt: List[Dict[str, str]], **kwargs) -> str:
        """
        提交提示并获取响应
        
        Args:
            prompt: 消息列表，每个消息包含role和content
            **kwargs: 额外参数，可以覆盖默认配置
        
        Returns:
            str: AI的响应内容
        """
        if not prompt:
            raise ValueError("提示不能为空")
        
        # 使用kwargs中的参数或默认值
        model = kwargs.get("model", self.model)
        temperature = kwargs.get("temperature", self.temperature)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        data = {
            "model": model,
            "messages": prompt,
            "temperature": temperature
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                error_msg = f"API请求失败: {response.status_code}"
                if response.text:
                    error_msg += f"\n响应: {response.text}"
                raise RuntimeError(error_msg)
                
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"网络请求失败: {e}")
        except (KeyError, IndexError) as e:
            raise RuntimeError(f"响应格式错误: {e}")


# 组合类：Gemini Balance Chat + ChromaDB Vector Store
class VannaGeminiBalance(ChromaDB_VectorStore, GeminiBalanceChat):
    """
    组合Gemini Balance Chat和ChromaDB Vector Store的完整Vanna实现
    """
    def __init__(self, config=None):
        if config is None:
            config = {}

        # 分离配置
        chroma_config = {k: v for k, v in config.items() if k.startswith('chroma_') or k in ['path']}
        gemini_config = {k: v for k, v in config.items() if k in ['base_url', 'api_key', 'model', 'temperature', 'timeout']}

        # 初始化两个父类
        ChromaDB_VectorStore.__init__(self, config=chroma_config)
        GeminiBalanceChat.__init__(self, config=gemini_config)


def test_gemini_balance_chat():
    """测试Gemini Balance Chat适配器"""

    print("🧪 测试 Gemini Balance Chat 适配器")
    print("=" * 50)

    # 配置
    config = {
        "base_url": "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com",
        "api_key": "12345",
        "model": "gemini-1.5-flash",
        "temperature": 0.7,
        "path": "./chroma_db"  # ChromaDB存储路径
    }

    try:
        # 初始化完整的Vanna实例
        vn = VannaGeminiBalance(config=config)
        print("✅ Vanna Gemini Balance 初始化成功")

        # 获取可用模型
        models = vn.get_available_models()
        print(f"✅ 可用模型: {models}")

        # 测试简单SQL生成（需要先训练一些数据）
        print("\n📚 添加一些训练数据...")

        # 添加DDL
        ddl = """
        CREATE TABLE customers (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT UNIQUE,
            sales_total DECIMAL(10,2)
        );
        """
        vn.add_ddl(ddl)
        print("✅ DDL训练数据已添加")

        # 添加问题-SQL对
        vn.add_question_sql(
            question="获取销售额最高的客户",
            sql="SELECT name, sales_total FROM customers ORDER BY sales_total DESC LIMIT 1;"
        )
        print("✅ 问题-SQL训练数据已添加")

        # 测试SQL生成
        question = "谁是销售额最高的客户？"
        sql = vn.generate_sql(question)
        print(f"✅ SQL生成测试成功:")
        print(f"   问题: {question}")
        print(f"   生成的SQL: {sql}")

        print("\n" + "=" * 50)
        print("🎯 所有测试通过！")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_gemini_balance_chat()
