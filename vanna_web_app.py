#!/usr/bin/env python3
"""
Vanna + Gemini Balance Web应用
提供完整的Web界面用于自然语言到SQL的转换
"""

import sys
sys.path.append('src')

from gemini_balance_chat import VannaGeminiBalance
from vanna.flask import <PERSON>na<PERSON>lask<PERSON><PERSON>

def setup_vanna_instance():
    """设置和配置Vanna实例"""
    
    print("🔧 配置Vanna实例...")
    
    # 配置信息
    config = {
        # Gemini Balance 服务配置
        "base_url": "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com",
        "api_key": "12345",
        "model": "gemini-2.5-pro",  # 使用最强的2.5 Pro模型
        "temperature": 0.7,
        
        # ChromaDB 向量存储配置
        "path": "./chroma_db_web"  # Web应用专用存储路径
    }
    
    # 初始化Vanna实例
    vn = VannaGeminiBalance(config=config)
    print("✅ Vanna实例初始化完成")
    
    # 添加示例数据库结构
    print("📚 添加示例数据库结构...")
    
    # 电商数据库结构
    ddl_statements = [
        """
        CREATE TABLE users (
            user_id INTEGER PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            full_name VARCHAR(100),
            age INTEGER,
            registration_date DATE,
            is_active BOOLEAN DEFAULT TRUE
        );
        """,
        """
        CREATE TABLE categories (
            category_id INTEGER PRIMARY KEY,
            category_name VARCHAR(50) NOT NULL,
            parent_category_id INTEGER,
            description TEXT
        );
        """,
        """
        CREATE TABLE products (
            product_id INTEGER PRIMARY KEY,
            product_name VARCHAR(100) NOT NULL,
            category_id INTEGER,
            brand VARCHAR(50),
            price DECIMAL(10,2) NOT NULL,
            cost DECIMAL(10,2),
            stock_quantity INTEGER DEFAULT 0,
            is_available BOOLEAN DEFAULT TRUE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(category_id)
        );
        """,
        """
        CREATE TABLE orders (
            order_id INTEGER PRIMARY KEY,
            user_id INTEGER,
            order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_amount DECIMAL(12,2),
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            order_status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled'),
            payment_method VARCHAR(50),
            shipping_address TEXT,
            FOREIGN KEY (user_id) REFERENCES users(user_id)
        );
        """,
        """
        CREATE TABLE order_items (
            item_id INTEGER PRIMARY KEY,
            order_id INTEGER,
            product_id INTEGER,
            quantity INTEGER NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            FOREIGN KEY (order_id) REFERENCES orders(order_id),
            FOREIGN KEY (product_id) REFERENCES products(product_id)
        );
        """,
        """
        CREATE TABLE reviews (
            review_id INTEGER PRIMARY KEY,
            product_id INTEGER,
            user_id INTEGER,
            rating INTEGER CHECK (rating >= 1 AND rating <= 5),
            review_text TEXT,
            review_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(product_id),
            FOREIGN KEY (user_id) REFERENCES users(user_id)
        );
        """
    ]
    
    for ddl in ddl_statements:
        vn.add_ddl(ddl.strip())
    
    print("✅ 数据库结构已添加")
    
    # 添加示例问题和SQL
    print("📝 添加示例问题和SQL...")
    
    examples = [
        {
            "question": "有多少个注册用户？",
            "sql": "SELECT COUNT(*) as total_users FROM users WHERE is_active = TRUE;"
        },
        {
            "question": "哪个产品类别最受欢迎？",
            "sql": """
            SELECT c.category_name, COUNT(oi.item_id) as total_orders
            FROM categories c
            JOIN products p ON c.category_id = p.category_id
            JOIN order_items oi ON p.product_id = oi.product_id
            GROUP BY c.category_id, c.category_name
            ORDER BY total_orders DESC
            LIMIT 1;
            """
        },
        {
            "question": "每月的销售趋势如何？",
            "sql": """
            SELECT 
                DATE_FORMAT(order_date, '%Y-%m') as sales_month,
                COUNT(order_id) as order_count,
                SUM(total_amount) as total_sales
            FROM orders
            WHERE order_status = 'delivered'
            GROUP BY sales_month
            ORDER BY sales_month;
            """
        },
        {
            "question": "评分最高的产品有哪些？",
            "sql": """
            SELECT 
                p.product_name,
                AVG(r.rating) as avg_rating,
                COUNT(r.review_id) as review_count
            FROM products p
            JOIN reviews r ON p.product_id = r.product_id
            GROUP BY p.product_id, p.product_name
            HAVING COUNT(r.review_id) >= 5
            ORDER BY avg_rating DESC
            LIMIT 10;
            """
        },
        {
            "question": "哪些用户是VIP客户（订单金额超过1000）？",
            "sql": """
            SELECT 
                u.username,
                u.full_name,
                SUM(o.total_amount) as total_spent
            FROM users u
            JOIN orders o ON u.user_id = o.user_id
            WHERE o.order_status = 'delivered'
            GROUP BY u.user_id, u.username, u.full_name
            HAVING total_spent > 1000
            ORDER BY total_spent DESC;
            """
        }
    ]
    
    for example in examples:
        vn.add_question_sql(example["question"], example["sql"])
    
    print("✅ 示例问题和SQL已添加")
    
    return vn

def create_web_app(vn):
    """创建Flask Web应用"""
    
    print("🌐 创建Web应用...")
    
    # 创建Flask应用
    app = VannaFlaskApp(
        vn=vn,
        debug=True,
        allow_llm_to_see_data=True,  # 允许LLM查看数据用于总结
        title="Vanna + Gemini Balance",
        subtitle="基于Gemini 2.5 Pro的智能SQL助手",
        logo="https://img.vanna.ai/vanna-flask.svg",
        show_training_data=True,
        suggested_questions=True,
        sql=True,
        table=True,
        csv_download=True,
        chart=True,
        redraw_chart=True,
        auto_fix_sql=True,
        ask_results_correct=True,
        followup_questions=True,
        summarization=True,
        function_generation=True
    )
    
    print("✅ Web应用创建完成")
    
    return app

def main():
    """主函数"""
    
    print("🚀 启动 Vanna + Gemini Balance Web应用")
    print("=" * 60)
    
    try:
        # 设置Vanna实例
        vn = setup_vanna_instance()
        
        # 创建Web应用
        app = create_web_app(vn)
        
        print("\n🌟 Web应用配置完成！")
        print("=" * 60)
        print("📋 功能特性:")
        print("   ✅ 自然语言到SQL转换")
        print("   ✅ 智能问题建议")
        print("   ✅ SQL执行和结果展示")
        print("   ✅ 数据可视化图表")
        print("   ✅ CSV数据下载")
        print("   ✅ 自动SQL修复")
        print("   ✅ 结果总结和后续问题")
        print("   ✅ 训练数据管理")
        print("\n🔗 访问地址:")
        print("   本地: http://localhost:8084")
        print("   网络: http://0.0.0.0:8084")
        print("\n💡 使用说明:")
        print("   1. 在Web界面中输入自然语言问题")
        print("   2. 系统会自动生成对应的SQL查询")
        print("   3. 可以查看和编辑生成的SQL")
        print("   4. 如果连接了数据库，可以直接执行SQL")
        print("   5. 支持数据可视化和结果下载")
        print("\n🛠️ 管理功能:")
        print("   - 可以添加新的数据库表结构")
        print("   - 可以添加问题-SQL训练示例")
        print("   - 可以查看和管理训练数据")
        print("\n" + "=" * 60)
        print("🎯 启动Web服务器...")
        
        # 启动Web应用
        app.run(
            host="0.0.0.0",  # 允许外部访问
            port=8084,       # 端口
            debug=True       # 调试模式
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 Web应用已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
