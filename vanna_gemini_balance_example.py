#!/usr/bin/env python3
"""
Vanna + Gemini Balance 使用示例
"""

import sys
sys.path.append('src')

# 导入我们创建的适配器
from gemini_balance_chat import VannaGeminiBalance

def main():
    """主函数 - 演示如何使用Vanna + Gemini Balance"""
    
    print("🚀 Vanna + Gemini Balance 使用示例")
    print("=" * 50)
    
    # 配置信息
    config = {
        # Gemini Balance 服务配置
        "base_url": "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com",
        "api_key": "12345",
        "model": "gemini-2.5-pro",  # 使用最强的2.5 Pro模型
        "temperature": 0.7,

        # ChromaDB 向量存储配置
        "path": "./chroma_db"  # 本地存储路径
    }
    
    # 初始化Vanna实例
    print("📦 初始化Vanna实例...")
    vn = VannaGeminiBalance(config=config)
    print("✅ 初始化完成")
    
    # 查看可用模型
    print("\n📋 可用的Gemini模型:")
    models = vn.get_available_models()
    for i, model in enumerate(models[:10], 1):  # 显示前10个
        print(f"   {i}. {model}")
    if len(models) > 10:
        print(f"   ... 还有 {len(models) - 10} 个模型")
    
    # 添加数据库结构信息
    print("\n📚 添加数据库结构信息...")
    
    # 添加表结构
    ddl_statements = [
        """
        CREATE TABLE users (
            id INTEGER PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            full_name VARCHAR(100),
            age INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """,
        """
        CREATE TABLE products (
            id INTEGER PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL,
            category VARCHAR(50),
            stock_quantity INTEGER DEFAULT 0
        );
        """,
        """
        CREATE TABLE orders (
            id INTEGER PRIMARY KEY,
            user_id INTEGER,
            order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_amount DECIMAL(10,2),
            status VARCHAR(20) DEFAULT 'pending',
            FOREIGN KEY (user_id) REFERENCES users(id)
        );
        """
    ]
    
    for ddl in ddl_statements:
        vn.add_ddl(ddl.strip())
    print("✅ 表结构信息已添加")
    
    # 添加示例问题和SQL
    print("\n📝 添加示例问题和SQL...")
    
    examples = [
        {
            "question": "获取所有用户信息",
            "sql": "SELECT * FROM users;"
        },
        {
            "question": "查找价格最高的产品",
            "sql": "SELECT name, price FROM products ORDER BY price DESC LIMIT 1;"
        },
        {
            "question": "统计每个用户的订单数量",
            "sql": "SELECT u.username, COUNT(o.id) as order_count FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.username;"
        },
        {
            "question": "获取待处理的订单",
            "sql": "SELECT * FROM orders WHERE status = 'pending';"
        }
    ]
    
    for example in examples:
        vn.add_question_sql(example["question"], example["sql"])
    print("✅ 示例问题和SQL已添加")
    
    # 测试SQL生成
    print("\n🧪 测试SQL生成...")
    
    test_questions = [
        "有多少个用户？",
        "哪个产品最贵？",
        "显示所有用户的订单总数",
        "查找年龄大于25岁的用户"
    ]
    
    for question in test_questions:
        print(f"\n❓ 问题: {question}")
        try:
            sql = vn.generate_sql(question)
            print(f"✅ 生成的SQL: {sql}")
        except Exception as e:
            print(f"❌ 生成失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 示例完成！")
    
    # 使用说明
    print("\n📖 使用说明:")
    print("1. 修改config中的base_url和api_key为你的实际值")
    print("2. 根据需要选择不同的model")
    print("3. 使用vn.add_ddl()添加数据库表结构")
    print("4. 使用vn.add_question_sql()添加问题-SQL示例")
    print("5. 使用vn.generate_sql()生成SQL查询")
    print("6. 如果连接了数据库，可以使用vn.run_sql()执行SQL")

if __name__ == "__main__":
    main()
