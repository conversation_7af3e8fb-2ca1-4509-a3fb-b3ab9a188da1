#!/usr/bin/env python3
"""
快速测试 gemini-balance 服务
"""

import requests
import json

def test_gemini_balance():
    """测试gemini-balance服务"""
    
    # 配置信息
    BASE_URL = "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    
    print("🧪 测试 gemini-balance 服务")
    print("=" * 40)
    print(f"服务地址: {BASE_URL}")
    print()
    
    # 测试1: 健康检查
    print("📡 测试1: 服务健康检查")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务运行正常")
        else:
            print(f"⚠️ 服务状态异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
    
    # 测试2: 获取模型列表
    print("\n📋 测试2: 获取模型列表")
    try:
        headers = {"Authorization": f"Bearer {AUTH_TOKEN}"}
        response = requests.get(f"{BASE_URL}/v1/models", headers=headers, timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            print("✅ 模型列表获取成功:")
            model_list = [model.get('id', 'unknown') for model in models.get("data", [])]

            # 显示所有模型
            print(f"   总共 {len(model_list)} 个模型:")
            for i, model in enumerate(model_list, 1):
                print(f"   {i:2d}. {model}")

            # 特别检查gemini-2.5-pro相关模型
            print("\n🔍 检查gemini-2.5-pro相关模型:")
            pro_25_models = [m for m in model_list if 'gemini-2.5-pro' in m]
            if pro_25_models:
                for model in pro_25_models:
                    print(f"   ✅ {model}")
            else:
                print("   ❌ 没有找到gemini-2.5-pro相关模型")
        else:
            print(f"❌ 获取模型失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 模型列表请求失败: {e}")
    
    # 测试3: 简单聊天
    print("\n💬 测试3: 简单聊天测试")
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {AUTH_TOKEN}"
        }
        
        data = {
            "model": "gemini-1.5-flash",
            "messages": [
                {"role": "user", "content": "请回复'测试成功'"}
            ]
        }
        
        response = requests.post(
            f"{BASE_URL}/v1/chat/completions", 
            headers=headers, 
            json=data, 
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print("✅ 聊天测试成功:")
            print(f"   AI回复: {content}")
        else:
            print(f"❌ 聊天测试失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 聊天请求失败: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 测试完成！")
    
    print("\n✅ 使用实际的API密钥进行测试")

if __name__ == "__main__":
    test_gemini_balance()
