#!/usr/bin/env python3
"""
测试正确的MySQL密码
"""

import pymysql

def test_connection():
    """测试MySQL连接"""
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123qwe',
        'charset': 'utf8mb4'
    }
    
    try:
        print("🔗 测试MySQL连接...")
        connection = pymysql.connect(**config)
        print("✅ 连接成功！")
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION();")
            version = cursor.fetchone()[0]
            print(f"MySQL版本: {version}")
            
            cursor.execute("SHOW DATABASES;")
            databases = cursor.fetchall()
            print(f"数据库列表 ({len(databases)}个):")
            for db in databases:
                print(f"  - {db[0]}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

if __name__ == "__main__":
    test_connection()
