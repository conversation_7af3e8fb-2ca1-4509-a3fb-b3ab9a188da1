#!/usr/bin/env python3
"""
测试MySQL连接
"""

import pymysql

def test_mysql_connections():
    """测试不同的MySQL连接配置"""
    
    print("🔍 测试MySQL连接...")
    
    # 常见的连接配置
    configs = [
        {
            'name': '默认配置 (root/root)',
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'root',
            'charset': 'utf8mb4'
        },
        {
            'name': '空密码配置 (root/空)',
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        },
        {
            'name': '常见密码配置 (root/123456)',
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '123456',
            'charset': 'utf8mb4'
        },
        {
            'name': '常见密码配置 (root/password)',
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'password',
            'charset': 'utf8mb4'
        }
    ]
    
    successful_config = None
    
    for config in configs:
        print(f"\n📝 测试: {config['name']}")
        try:
            # 尝试连接
            connection = pymysql.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                charset=config['charset']
            )
            
            print("✅ 连接成功！")
            
            # 获取基本信息
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION();")
                version = cursor.fetchone()[0]
                print(f"   MySQL版本: {version}")
                
                cursor.execute("SHOW DATABASES;")
                databases = cursor.fetchall()
                print(f"   数据库列表 ({len(databases)}个):")
                for db in databases:
                    print(f"     - {db[0]}")
            
            connection.close()
            successful_config = config
            break
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
    
    if successful_config:
        print(f"\n🎯 成功的连接配置:")
        print(f"   主机: {successful_config['host']}")
        print(f"   端口: {successful_config['port']}")
        print(f"   用户: {successful_config['user']}")
        print(f"   密码: {'*' * len(successful_config['password']) if successful_config['password'] else '(空)'}")
        
        # 选择一个数据库进行测试
        print(f"\n🔍 选择数据库进行详细测试...")
        test_databases = ['test', 'mysql', 'information_schema', 'performance_schema']
        
        for db_name in test_databases:
            try:
                test_config = successful_config.copy()
                test_config['database'] = db_name
                
                connection = pymysql.connect(**test_config)
                print(f"✅ 可以连接到数据库: {db_name}")
                
                with connection.cursor() as cursor:
                    cursor.execute("SHOW TABLES;")
                    tables = cursor.fetchall()
                    print(f"   表数量: {len(tables)}")
                    if tables:
                        print(f"   示例表: {[t[0] for t in tables[:5]]}")
                
                connection.close()
                
                # 如果是非系统数据库且有表，推荐使用
                if db_name not in ['information_schema', 'performance_schema', 'sys'] and tables:
                    print(f"🌟 推荐使用数据库: {db_name}")
                    successful_config['database'] = db_name
                    break
                    
            except Exception as e:
                print(f"❌ 无法连接到数据库 {db_name}: {e}")
        
        return successful_config
    else:
        print("\n❌ 所有连接配置都失败了")
        print("💡 请检查:")
        print("   1. MySQL容器是否正在运行")
        print("   2. 端口3306是否正确映射")
        print("   3. 用户名和密码是否正确")
        return None

if __name__ == "__main__":
    test_mysql_connections()
