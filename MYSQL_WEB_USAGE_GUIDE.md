# Vanna + Gemini Balance + MySQL Web使用指南

## 🎉 恭喜！你的Web应用已成功部署

你的Vanna Web应用现在已经连接到MySQL数据库，可以进行实时的自然语言到SQL转换和数据查询。

## 🔗 访问信息

- **Web界面**: http://localhost:8084
- **数据库**: MySQL 8.0 (whodb_test)
- **AI模型**: Gemini 2.5 Pro
- **表数量**: 4个表，包含完整的电商数据

## 📊 数据库结构

### 表概览
1. **users** (5条记录) - 用户信息
2. **products** (8条记录) - 产品信息
3. **orders** (5条记录) - 订单信息
4. **order_items** (9条记录) - 订单项详情

### 详细结构

#### users 表
- `id` - 用户ID
- `username` - 用户名
- `email` - 邮箱
- `full_name` - 全名
- `age` - 年龄
- `created_at` - 创建时间
- `updated_at` - 更新时间
- `is_active` - 是否活跃
- `profile_data` - JSON格式的个人资料

#### products 表
- `id` - 产品ID
- `name` - 产品名称
- `description` - 产品描述
- `price` - 价格
- `category` - 类别
- `stock_quantity` - 库存数量
- `created_at` - 创建时间
- `is_available` - 是否可用

#### orders 表
- `id` - 订单ID
- `user_id` - 用户ID (外键)
- `order_date` - 订单日期
- `total_amount` - 总金额
- `status` - 订单状态 (pending/processing/shipped/delivered/cancelled)
- `shipping_address` - 配送地址
- `notes` - 备注

#### order_items 表
- `id` - 订单项ID
- `order_id` - 订单ID (外键)
- `product_id` - 产品ID (外键)
- `quantity` - 数量
- `unit_price` - 单价
- `total_price` - 总价

## 🎯 示例问题

### 基础查询
```
有多少个用户？
有哪些产品？
显示所有订单
最新的5个订单是什么？
```

### 用户分析
```
年龄最大的用户是谁？
有多少活跃用户？
用户的平均年龄是多少？
哪些用户还没有下过订单？
```

### 产品分析
```
最贵的产品是什么？
哪个类别的产品最多？
库存最少的产品有哪些？
平均产品价格是多少？
```

### 订单分析
```
总销售额是多少？
哪个用户下单最多？
平均订单金额是多少？
已发货的订单有哪些？
每个月的销售趋势如何？
```

### 复杂分析
```
每个用户的总消费金额
最受欢迎的产品排行榜
每个产品的销售数量
订单状态分布情况
用户购买行为分析
```

### 业务洞察
```
哪些产品需要补货？
VIP客户有哪些？（消费超过1000元）
哪些订单可能有问题？
产品销售排行榜
用户活跃度分析
```

## 🌟 Web界面功能

### 核心功能
- ✅ **自然语言输入** - 用中文或英文提问
- ✅ **智能SQL生成** - 基于Gemini 2.5 Pro
- ✅ **实时数据执行** - 直接在你的MySQL数据库中执行
- ✅ **结果展示** - 表格形式显示查询结果

### 高级功能
- ✅ **数据可视化** - 自动生成图表和图形
- ✅ **CSV下载** - 下载查询结果到本地
- ✅ **SQL编辑** - 手动编辑和优化生成的SQL
- ✅ **自动修复** - 智能修复SQL语法错误

### AI增强功能
- ✅ **结果总结** - AI总结查询结果的关键信息
- ✅ **后续问题** - 基于当前结果生成相关问题
- ✅ **智能建议** - 提供相关的查询建议

### 管理功能
- ✅ **训练数据** - 查看和管理所有训练数据
- ✅ **表结构** - 自动发现的数据库表结构
- ✅ **查询历史** - 查看之前的查询记录

## 💡 使用技巧

### 1. 提问技巧
- 使用自然语言，就像和人对话一样
- 可以使用中文或英文
- 尽量具体描述你想要的信息
- 可以使用业务术语，如"销售额"、"客户"等

### 2. 查看结果
- 查询结果会以表格形式显示
- 可以点击下载按钮保存为CSV文件
- 如果数据适合，会自动生成图表
- 可以查看AI对结果的总结

### 3. 优化查询
- 如果SQL不准确，可以手动编辑
- 可以点击"修复SQL"让AI自动优化
- 查看建议的后续问题获得更多洞察

### 4. 探索数据
- 从简单问题开始，如"有多少用户"
- 逐步深入，如"每个用户的消费情况"
- 利用AI建议发现新的分析角度

## 🔧 故障排除

### 常见问题
1. **查询失败** - 检查问题描述是否清晰
2. **结果为空** - 确认数据库中有相关数据
3. **SQL错误** - 使用自动修复功能或手动编辑

### 性能优化
- 对于大量数据，使用LIMIT限制结果数量
- 复杂查询可能需要更多时间
- 可以先用简单查询验证数据

## 📈 数据洞察示例

基于你的电商数据，可以探索：

1. **用户行为**
   - 用户购买偏好
   - 复购率分析
   - 用户生命周期价值

2. **产品表现**
   - 热销产品排行
   - 库存周转率
   - 价格敏感度分析

3. **订单趋势**
   - 销售季节性
   - 订单完成率
   - 平均订单价值

4. **业务指标**
   - 收入增长趋势
   - 客户获取成本
   - 产品利润分析

---

## 🎯 开始使用

1. 打开浏览器访问 http://localhost:8084
2. 在输入框中输入你的问题
3. 点击发送或按回车
4. 查看生成的SQL和执行结果
5. 探索AI建议的后续问题

**祝你使用愉快！享受AI驱动的数据分析体验！** 🚀
