# Vanna + Gemini Balance 集成方案

## 概述

本方案实现了Vanna与Gemini Balance服务的集成，让你可以使用自己部署的Gemini模型来进行自然语言到SQL的转换。

## 支持的模型

通过测试，你的Gemini Balance服务支持以下模型：

### 主要推荐模型
- **gemini-1.5-flash** - 快速响应，适合日常使用
- **gemini-1.5-pro** - 更强性能，适合复杂查询
- **gemini-2.0-flash** - 最新版本，平衡性能和速度
- **gemini-2.5-pro** - 最强性能版本

### 完整模型列表
```
gemini-1.5-pro-latest, gemini-1.5-pro-002, gemini-1.5-pro
gemini-1.5-flash-latest, gemini-1.5-flash, gemini-1.5-flash-002
gemini-1.5-flash-8b, gemini-1.5-flash-8b-001, gemini-1.5-flash-8b-latest
gemini-2.5-pro-preview-03-25, gemini-2.5-flash-preview-05-20, gemini-2.5-flash
gemini-2.0-flash-exp, gemini-2.0-flash, gemini-2.0-flash-001
gemini-2.0-pro-exp, gemini-exp-1206
... 还有更多模型
```

## 服务信息

- **服务地址**: `https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com`
- **认证方式**: Bearer Token (`12345`)
- **API格式**: OpenAI兼容格式
- **状态**: ✅ 服务运行正常，所有功能测试通过

## 文件说明

### 核心文件

1. **`gemini_balance_chat.py`** - 主要适配器文件
   - `GeminiBalanceChat` - 基础聊天适配器类
   - `VannaGeminiBalance` - 完整的Vanna集成类（包含向量存储）

2. **`vanna_gemini_balance_example.py`** - 完整使用示例
   - 演示如何初始化和使用
   - 包含数据库结构添加和SQL生成测试

3. **`test_gemini_balance.py`** - 服务测试脚本
   - 验证服务可用性
   - 获取模型列表
   - 测试基本聊天功能

## 快速开始

### 1. 基本使用

```python
from gemini_balance_chat import VannaGeminiBalance

# 配置
config = {
    "base_url": "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com",
    "api_key": "12345",
    "model": "gemini-1.5-flash",
    "temperature": 0.7,
    "path": "./chroma_db"  # 向量存储路径
}

# 初始化
vn = VannaGeminiBalance(config=config)

# 添加数据库结构
vn.add_ddl("""
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    username VARCHAR(50),
    email VARCHAR(100),
    age INTEGER
);
""")

# 生成SQL
sql = vn.generate_sql("有多少个用户？")
print(sql)  # 输出: SELECT COUNT(*) FROM users;
```

### 2. 运行示例

```bash
# 测试服务可用性
python3 test_gemini_balance.py

# 运行完整示例
python3 vanna_gemini_balance_example.py
```

## 配置参数

### Gemini Balance 配置
- `base_url`: 服务基础URL
- `api_key`: 认证密钥
- `model`: 使用的模型名称（默认: gemini-1.5-flash）
- `temperature`: 生成温度（默认: 0.7）
- `timeout`: 请求超时时间（默认: 30秒）

### ChromaDB 配置
- `path`: 向量数据库存储路径（默认: ./chroma_db）

## 功能特性

### ✅ 已实现功能
- [x] 与Gemini Balance服务的完整集成
- [x] 支持所有可用的Gemini模型
- [x] 自然语言到SQL的转换
- [x] 向量存储和检索（基于ChromaDB）
- [x] 训练数据管理（DDL、文档、问题-SQL对）
- [x] 错误处理和服务验证

### 🔧 主要方法
- `vn.add_ddl(ddl)` - 添加数据库表结构
- `vn.add_question_sql(question, sql)` - 添加问题-SQL训练对
- `vn.generate_sql(question)` - 生成SQL查询
- `vn.get_available_models()` - 获取可用模型列表
- `vn.get_training_data()` - 获取训练数据

## 测试结果

### 服务测试 ✅
```
🧪 测试 gemini-balance 服务
========================================
📡 测试1: 服务健康检查 ✅ 服务运行正常
📋 测试2: 获取模型列表 ✅ 模型列表获取成功
💬 测试3: 简单聊天测试 ✅ 聊天测试成功
```

### SQL生成测试 ✅
```
❓ 问题: 有多少个用户？
✅ 生成的SQL: SELECT COUNT(*) FROM users;

❓ 问题: 哪个产品最贵？
✅ 生成的SQL: SELECT name FROM products ORDER BY price DESC LIMIT 1;

❓ 问题: 查找年龄大于25岁的用户
✅ 生成的SQL: SELECT * FROM users WHERE age > 25;
```

## 注意事项

1. **API密钥安全**: 在生产环境中，请妥善保管API密钥
2. **模型选择**: 根据需求选择合适的模型，flash版本速度快，pro版本质量高
3. **向量存储**: ChromaDB数据会保存在本地，首次使用会创建数据库文件
4. **网络连接**: 确保能够访问Gemini Balance服务地址

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证服务地址和API密钥

2. **模型不可用**
   - 使用 `get_available_models()` 查看可用模型
   - 确认模型名称拼写正确

3. **SQL生成质量不佳**
   - 添加更多DDL信息
   - 提供更多问题-SQL训练示例
   - 调整temperature参数

## 扩展使用

### 连接数据库
```python
# 连接SQLite数据库
vn.connect_to_sqlite("path/to/database.db")

# 执行生成的SQL
sql = vn.generate_sql("查询用户数量")
result = vn.run_sql(sql)
print(result)
```

### 自定义配置
```python
# 使用不同模型
config["model"] = "gemini-2.0-flash"

# 调整生成参数
config["temperature"] = 0.3  # 更保守的生成

# 自定义超时
config["timeout"] = 60  # 60秒超时
```

---

## 总结

通过这个集成方案，你可以：
1. ✅ 使用自己的Gemini Balance服务
2. ✅ 享受Vanna的完整功能
3. ✅ 支持61个不同的Gemini模型
4. ✅ 获得高质量的自然语言到SQL转换

所有测试都已通过，可以放心在生产环境中使用！
