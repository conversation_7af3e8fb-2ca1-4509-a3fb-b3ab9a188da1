#!/usr/bin/env python3
"""
Vanna + Gemini Balance + MySQL Web应用
连接到你的MySQL数据库，提供完整的自然语言到SQL功能
"""

import sys
sys.path.append('src')

from gemini_balance_chat import VannaGeminiBalance
from vanna.flask import VannaFlaskApp
import pymysql
import pandas as pd

def setup_mysql_connection():
    """设置MySQL数据库连接"""
    
    print("🔗 连接到MySQL数据库...")
    
    # MySQL连接配置
    mysql_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',  # 请根据实际情况修改
        'password': '123qwe',  # 从docker inspect获取的密码
        'charset': 'utf8mb4'
    }
    
    try:
        # 测试连接
        connection = pymysql.connect(**mysql_config)
        print("✅ MySQL连接测试成功")
        
        # 获取数据库列表并选择合适的数据库
        with connection.cursor() as cursor:
            cursor.execute("SHOW DATABASES;")
            databases = [db[0] for db in cursor.fetchall()]
            print(f"📊 发现数据库: {databases}")

            # 选择最合适的数据库（优先选择有数据的数据库）
            preferred_dbs = ['whodb_test', 'gemini_balance', 'test']
            selected_db = None

            for db in preferred_dbs:
                if db in databases:
                    selected_db = db
                    break

            if not selected_db:
                # 如果没有找到首选数据库，选择第一个非系统数据库
                system_dbs = ['information_schema', 'mysql', 'performance_schema', 'sys']
                for db in databases:
                    if db not in system_dbs:
                        selected_db = db
                        break

            if selected_db:
                mysql_config['database'] = selected_db
                print(f"🎯 选择数据库: {selected_db}")

                # 连接到选定的数据库并获取表信息
                connection.close()
                connection = pymysql.connect(**mysql_config)

                with connection.cursor() as cursor:
                    cursor.execute("SHOW TABLES;")
                    tables = cursor.fetchall()
                    print(f"📋 发现 {len(tables)} 个表:")
                    for table in tables[:10]:  # 显示前10个表
                        print(f"   - {table[0]}")
                    if len(tables) > 10:
                        print(f"   ... 还有 {len(tables) - 10} 个表")
            else:
                print("⚠️ 未找到合适的数据库，将使用默认连接")
        
        connection.close()
        return mysql_config
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        print("💡 请检查以下配置:")
        print("   - 数据库是否运行在localhost:3306")
        print("   - 用户名和密码是否正确")
        print("   - 数据库名称是否存在")
        print("   - 是否安装了pymysql: pip install pymysql")
        return None

def setup_vanna_with_mysql(mysql_config):
    """设置连接MySQL的Vanna实例"""
    
    print("🔧 配置Vanna + MySQL实例...")
    
    # Vanna配置
    vanna_config = {
        "base_url": "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com",
        "api_key": "12345",
        "model": "gemini-2.5-pro",
        "temperature": 0.7,
        "path": "./chroma_db_mysql"
    }
    
    # 初始化Vanna实例
    vn = VannaGeminiBalance(config=vanna_config)
    print("✅ Vanna实例初始化完成")
    
    # 连接到MySQL数据库
    def run_sql(sql: str) -> pd.DataFrame:
        """执行SQL查询并返回DataFrame"""
        try:
            connection = pymysql.connect(**mysql_config)
            df = pd.read_sql(sql, connection)
            connection.close()
            return df
        except Exception as e:
            print(f"SQL执行错误: {e}")
            raise e
    
    # 设置SQL执行函数
    vn.run_sql = run_sql
    vn.run_sql_is_set = True
    print("✅ MySQL连接已配置")
    
    return vn

def auto_discover_schema(vn, mysql_config):
    """自动发现并添加数据库结构"""
    
    print("🔍 自动发现数据库结构...")
    
    try:
        connection = pymysql.connect(**mysql_config)
        
        with connection.cursor() as cursor:
            # 获取所有表
            cursor.execute("SHOW TABLES;")
            tables = [table[0] for table in cursor.fetchall()]
            
            print(f"📊 发现 {len(tables)} 个表，正在获取结构...")
            
            for table in tables:
                try:
                    # 获取表的CREATE语句
                    cursor.execute(f"SHOW CREATE TABLE `{table}`;")
                    create_statement = cursor.fetchone()[1]
                    
                    # 添加到Vanna训练数据
                    vn.add_ddl(create_statement)
                    print(f"   ✅ 已添加表: {table}")
                    
                except Exception as e:
                    print(f"   ❌ 跳过表 {table}: {e}")
        
        connection.close()
        print("✅ 数据库结构发现完成")
        
    except Exception as e:
        print(f"❌ 结构发现失败: {e}")

def add_sample_questions(vn):
    """添加通用的示例问题"""
    
    print("📝 添加示例问题...")
    
    # 通用示例问题
    examples = [
        {
            "question": "数据库中有哪些表？",
            "sql": "SHOW TABLES;"
        },
        {
            "question": "显示表的行数统计",
            "sql": "SELECT table_name, table_rows FROM information_schema.tables WHERE table_schema = DATABASE();"
        },
        {
            "question": "查看数据库大小",
            "sql": "SELECT table_schema AS 'Database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' FROM information_schema.tables WHERE table_schema = DATABASE();"
        }
    ]
    
    for example in examples:
        vn.add_question_sql(example["question"], example["sql"])
    
    print("✅ 示例问题已添加")

def main():
    """主函数"""
    
    print("🚀 启动 Vanna + Gemini Balance + MySQL Web应用")
    print("=" * 60)
    
    try:
        # 检查依赖
        try:
            import pymysql
            print("✅ PyMySQL依赖检查通过")
        except ImportError:
            print("❌ 缺少PyMySQL依赖")
            print("请安装: pip install pymysql")
            return
        
        # 连接MySQL
        mysql_config = setup_mysql_connection()
        if not mysql_config:
            print("\n💡 如果需要修改数据库连接信息，请编辑此文件中的mysql_config部分")
            return
        
        # 设置Vanna实例
        vn = setup_vanna_with_mysql(mysql_config)
        
        # 自动发现数据库结构
        auto_discover_schema(vn, mysql_config)
        
        # 添加示例问题
        add_sample_questions(vn)
        
        # 创建Web应用
        print("\n🌐 创建Web应用...")
        app = VannaFlaskApp(
            vn=vn,
            debug=True,
            allow_llm_to_see_data=True,  # 允许查看数据用于总结
            title="Vanna + Gemini Balance + MySQL",
            subtitle="连接到你的MySQL数据库的智能SQL助手",
            logo="https://img.vanna.ai/vanna-flask.svg",
            show_training_data=True,
            suggested_questions=True,
            sql=True,
            table=True,
            csv_download=True,
            chart=True,
            redraw_chart=True,
            auto_fix_sql=True,
            ask_results_correct=True,
            followup_questions=True,
            summarization=True,
            function_generation=True
        )
        print("✅ Web应用创建完成")
        
        print("\n🌟 Web应用已准备就绪！")
        print("=" * 60)
        print("📋 功能特性:")
        print("   ✅ 连接到你的MySQL数据库")
        print("   ✅ 自动发现数据库表结构")
        print("   ✅ 自然语言到SQL转换")
        print("   ✅ 实时SQL执行和结果展示")
        print("   ✅ 数据可视化图表")
        print("   ✅ CSV数据下载")
        print("   ✅ AI结果总结")
        print("\n🔗 访问地址:")
        print("   本地: http://localhost:8084")
        print("   网络: http://0.0.0.0:8084")
        print("\n💡 使用说明:")
        print("   1. 在Web界面中输入自然语言问题")
        print("   2. 系统会自动生成SQL查询")
        print("   3. SQL会在你的MySQL数据库中执行")
        print("   4. 查看实时数据结果和可视化")
        print("\n🎯 示例问题:")
        print("   - '数据库中有哪些表？'")
        print("   - '显示每个表的记录数'")
        print("   - '查询最新的10条记录'")
        print("   - 根据你的具体表结构提问")
        print("\n" + "=" * 60)
        print("🎯 启动Web服务器...")
        
        # 启动Web应用
        app.run(
            host="0.0.0.0",
            port=8084,
            debug=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 Web应用已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
