#!/usr/bin/env python3
"""
测试 Gemini 2.5 Pro 模型
"""

import sys
sys.path.append('src')

from gemini_balance_chat import VannaGeminiBalance

def test_gemini_25_pro():
    """测试Gemini 2.5 Pro模型的性能"""
    
    print("🧪 测试 Gemini 2.5 Pro 模型")
    print("=" * 50)
    
    # 配置使用gemini-2.5-pro
    config = {
        "base_url": "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com",
        "api_key": "12345",
        "model": "gemini-2.5-pro",  # 使用最强的2.5 Pro模型
        "temperature": 0.7,
        "path": "./chroma_db_25pro"
    }
    
    try:
        # 初始化
        print("📦 初始化Vanna + Gemini 2.5 Pro...")
        vn = VannaGeminiBalance(config=config)
        print("✅ 初始化成功")
        
        # 添加复杂的数据库结构
        print("\n📚 添加复杂数据库结构...")
        
        complex_ddl = """
        CREATE TABLE customers (
            customer_id INTEGER PRIMARY KEY,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100) UNIQUE,
            phone VARCHAR(20),
            registration_date DATE,
            customer_tier ENUM('bronze', 'silver', 'gold', 'platinum'),
            total_spent DECIMAL(12,2) DEFAULT 0.00
        );
        
        CREATE TABLE products (
            product_id INTEGER PRIMARY KEY,
            product_name VARCHAR(100) NOT NULL,
            category VARCHAR(50),
            subcategory VARCHAR(50),
            brand VARCHAR(50),
            price DECIMAL(10,2) NOT NULL,
            cost DECIMAL(10,2),
            stock_quantity INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE orders (
            order_id INTEGER PRIMARY KEY,
            customer_id INTEGER,
            order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_amount DECIMAL(12,2),
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            tax_amount DECIMAL(10,2),
            shipping_cost DECIMAL(8,2),
            order_status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled'),
            payment_method VARCHAR(50),
            shipping_address TEXT,
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
        );
        
        CREATE TABLE order_items (
            item_id INTEGER PRIMARY KEY,
            order_id INTEGER,
            product_id INTEGER,
            quantity INTEGER NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            discount_percent DECIMAL(5,2) DEFAULT 0.00,
            FOREIGN KEY (order_id) REFERENCES orders(order_id),
            FOREIGN KEY (product_id) REFERENCES products(product_id)
        );
        """
        
        vn.add_ddl(complex_ddl)
        print("✅ 复杂数据库结构已添加")
        
        # 添加一些复杂的问题-SQL示例
        print("\n📝 添加复杂查询示例...")
        
        complex_examples = [
            {
                "question": "计算每个客户等级的平均订单金额",
                "sql": """
                SELECT 
                    c.customer_tier,
                    AVG(o.total_amount) as avg_order_amount,
                    COUNT(o.order_id) as order_count
                FROM customers c
                LEFT JOIN orders o ON c.customer_id = o.customer_id
                GROUP BY c.customer_tier
                ORDER BY avg_order_amount DESC;
                """
            },
            {
                "question": "找出最受欢迎的产品类别及其销售额",
                "sql": """
                SELECT 
                    p.category,
                    SUM(oi.quantity * oi.unit_price) as total_sales,
                    SUM(oi.quantity) as total_quantity_sold
                FROM products p
                JOIN order_items oi ON p.product_id = oi.product_id
                JOIN orders o ON oi.order_id = o.order_id
                WHERE o.order_status = 'delivered'
                GROUP BY p.category
                ORDER BY total_sales DESC;
                """
            }
        ]
        
        for example in complex_examples:
            vn.add_question_sql(example["question"], example["sql"])
        print("✅ 复杂查询示例已添加")
        
        # 测试复杂SQL生成
        print("\n🧪 测试复杂SQL生成...")
        
        complex_questions = [
            "哪些白金客户在过去30天内没有下单？",
            "计算每个品牌的利润率（基于价格和成本）",
            "找出订单金额超过平均值2倍的异常订单",
            "分析每月的销售趋势，包括订单数量和总金额",
            "哪些产品的库存周转率最低？"
        ]
        
        for question in complex_questions:
            print(f"\n❓ 复杂问题: {question}")
            try:
                sql = vn.generate_sql(question)
                print(f"✅ 生成的SQL:")
                print(f"   {sql}")
            except Exception as e:
                print(f"❌ 生成失败: {e}")
        
        print("\n" + "=" * 50)
        print("🎯 Gemini 2.5 Pro 测试完成！")
        print("💡 Gemini 2.5 Pro 展现了强大的复杂查询理解和生成能力")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gemini_25_pro()
