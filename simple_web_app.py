#!/usr/bin/env python3
"""
简化版Vanna Web应用
"""

import sys
sys.path.append('src')

from gemini_balance_chat import VannaGeminiBalance

def setup_simple_vanna():
    """设置简单的Vanna实例"""
    
    config = {
        "base_url": "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com",
        "api_key": "12345",
        "model": "gemini-2.5-pro",
        "temperature": 0.7,
        "path": "./chroma_db_simple"
    }
    
    vn = VannaGeminiBalance(config=config)
    
    # 添加基本的表结构
    vn.add_ddl("""
    CREATE TABLE users (
        id INTEGER PRIMARY KEY,
        name VARCHAR(100),
        email VARCHAR(100),
        age INTEGER
    );
    """)
    
    vn.add_ddl("""
    CREATE TABLE orders (
        id INTEGER PRIMARY KEY,
        user_id INTEGER,
        amount DECIMAL(10,2),
        order_date DATE,
        FOREIGN KEY (user_id) REFERENCES users(id)
    );
    """)
    
    # 添加示例问题
    vn.add_question_sql("有多少用户", "SELECT COUNT(*) FROM users;")
    vn.add_question_sql("总订单金额", "SELECT SUM(amount) FROM orders;")
    
    return vn

def main():
    """主函数"""
    
    print("🚀 启动简化版Vanna Web应用")
    print("=" * 50)
    
    try:
        # 检查Flask依赖
        try:
            from vanna.flask import VannaFlaskApp
            print("✅ Flask依赖检查通过")
        except ImportError as e:
            print(f"❌ Flask依赖缺失: {e}")
            print("请安装: pip install flask flask-sock flasgger")
            return
        
        # 设置Vanna
        print("📦 设置Vanna实例...")
        vn = setup_simple_vanna()
        print("✅ Vanna设置完成")
        
        # 创建Web应用
        print("🌐 创建Web应用...")
        app = VannaFlaskApp(
            vn=vn,
            debug=True,
            allow_llm_to_see_data=False,
            title="Vanna + Gemini Balance (简化版)",
            subtitle="基于Gemini 2.5 Pro的SQL助手"
        )
        print("✅ Web应用创建完成")
        
        print("\n🌟 Web应用已准备就绪！")
        print("🔗 访问地址: http://localhost:8084")
        print("💡 在浏览器中打开上述地址即可使用")
        print("\n🎯 启动服务器...")
        
        # 启动应用
        app.run(host="0.0.0.0", port=8084, debug=False)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
