#!/usr/bin/env python3
"""
检查所有数据库的内容
"""

import pymysql

def check_all_databases():
    """检查所有数据库的表和数据"""
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123qwe',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**config)
        
        with connection.cursor() as cursor:
            cursor.execute("SHOW DATABASES;")
            databases = [db[0] for db in cursor.fetchall()]
            
            # 排除系统数据库
            user_databases = [db for db in databases if db not in 
                            ['information_schema', 'mysql', 'performance_schema', 'sys']]
            
            print(f"🔍 检查用户数据库: {user_databases}")
            
            for db_name in user_databases:
                print(f"\n📊 数据库: {db_name}")
                print("=" * 40)
                
                try:
                    cursor.execute(f"USE {db_name};")
                    cursor.execute("SHOW TABLES;")
                    tables = cursor.fetchall()
                    
                    if not tables:
                        print("   📋 无表")
                        continue
                    
                    print(f"   📋 表数量: {len(tables)}")
                    
                    for table in tables:
                        table_name = table[0]
                        print(f"\n   📄 表: {table_name}")
                        
                        # 获取表结构
                        cursor.execute(f"DESCRIBE {table_name};")
                        columns = cursor.fetchall()
                        print(f"      列数: {len(columns)}")
                        for col in columns[:5]:  # 显示前5列
                            print(f"        - {col[0]} ({col[1]})")
                        if len(columns) > 5:
                            print(f"        ... 还有 {len(columns) - 5} 列")
                        
                        # 获取记录数
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                        count = cursor.fetchone()[0]
                        print(f"      记录数: {count}")
                        
                        # 如果有数据，显示几条示例
                        if count > 0:
                            cursor.execute(f"SELECT * FROM {table_name} LIMIT 3;")
                            samples = cursor.fetchall()
                            print(f"      示例数据:")
                            for i, sample in enumerate(samples, 1):
                                print(f"        {i}. {sample}")
                
                except Exception as e:
                    print(f"   ❌ 检查失败: {e}")
        
        connection.close()
        
        # 推荐最佳数据库
        print(f"\n🎯 推荐使用的数据库:")
        if 'whodb_test' in user_databases:
            print("   1. whodb_test (可能包含测试数据)")
        if 'gemini_balance' in user_databases:
            print("   2. gemini_balance (项目相关)")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_all_databases()
